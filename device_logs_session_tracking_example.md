# Device Logs Session-Based Position Tracking

## Overview

The `/device_logs` endpoint now supports automatic session-based position tracking, eliminating the need for clients to manually track log positions across multiple requests within the same session.

## How It Works

### Automatic Position Continuation
- **First call**: Starts from position 0
- **Subsequent calls**: Automatically continues from the previous `end_pos` within the same session
- **Explicit override**: Providing `start_pos` parameter overrides automatic tracking

### Session Isolation
- Each `session_id` + `device` combination maintains its own position tracking
- Different sessions can read the same device logs independently
- Different devices within the same session maintain separate positions

## API Usage Examples

### Example 1: Basic Session Tracking
```bash
# First call - starts from beginning
GET /device_logs?session_id=abc123&device=pixel_5
Response: {
  "meta": {
    "start_pos": 0,
    "end_pos": 1000,
    "session_tracking": true
  },
  "value": [...]
}

# Second call - automatically continues from position 1000
GET /device_logs?session_id=abc123&device=pixel_5
Response: {
  "meta": {
    "start_pos": 1000,
    "end_pos": 2500,
    "session_tracking": true
  },
  "value": [...]
}

# Third call - continues from position 2500
GET /device_logs?session_id=abc123&device=pixel_5
Response: {
  "meta": {
    "start_pos": 2500,
    "end_pos": 3200,
    "session_tracking": true
  },
  "value": [...]
}
```

### Example 2: Explicit Position Override
```bash
# Override automatic tracking by providing start_pos
GET /device_logs?session_id=abc123&device=pixel_5&start_pos=500
Response: {
  "meta": {
    "start_pos": 500,
    "end_pos": 1800,
    "session_tracking": true
  },
  "value": [...]
}

# Next call continues from the new position (1800)
GET /device_logs?session_id=abc123&device=pixel_5
Response: {
  "meta": {
    "start_pos": 1800,
    "end_pos": 2900,
    "session_tracking": true
  },
  "value": [...]
}
```

### Example 3: Multiple Sessions
```bash
# Session 1
GET /device_logs?session_id=session1&device=pixel_5
# Returns: start_pos=0, end_pos=1000

# Session 2 (independent tracking)
GET /device_logs?session_id=session2&device=pixel_5
# Returns: start_pos=0, end_pos=1000

# Session 1 continues
GET /device_logs?session_id=session1&device=pixel_5
# Returns: start_pos=1000, end_pos=2000

# Session 2 continues independently
GET /device_logs?session_id=session2&device=pixel_5
# Returns: start_pos=1000, end_pos=2000
```

## Implementation Details

### Storage Mechanism
- Position data is stored in files: `{STATE_FILES_DIR}/device_logs_position_{session_id}_{device}`
- File contains the last `end_pos` as a simple integer
- Persistent across server restarts

### Automatic Cleanup
- Position files older than 24 hours are automatically cleaned up
- Cleanup runs probabilistically (1 in 100 requests) to avoid performance impact
- Cleanup runs in background thread to avoid blocking requests

### Error Handling
- Missing or corrupted position files default to position 0
- File I/O errors are logged but don't break the endpoint
- Graceful degradation ensures the endpoint always works

### Thread Safety
- File-based storage provides natural atomicity for single writes
- Each session+device combination uses a separate file
- No shared state between concurrent requests

## Benefits

1. **Simplified Client Code**: No need to track positions manually
2. **Reduced Errors**: Eliminates position tracking bugs in client applications
3. **Better UX**: Seamless log streaming experience
4. **Backward Compatible**: Explicit `start_pos` still works as before
5. **Session Isolation**: Multiple sessions don't interfere with each other

## Response Format Changes

The response now includes a `session_tracking: true` field in the meta object to indicate that automatic position tracking is active:

```json
{
  "meta": {
    "start_pos": 1000,
    "end_pos": 2500,
    "session_tracking": true
  },
  "value": [
    {
      "timestamp": 1640995242130,
      "level": "ALL",
      "message": "03-26 19:20:42.130 I/TestRunner(21239): Log message"
    }
  ]
}
```

## Edge Cases Handled

1. **Log File Rotation**: If log file is truncated/rotated, stored position gracefully handles out-of-bounds seeks
2. **Session Cleanup**: Old session data is automatically cleaned up
3. **Concurrent Access**: Multiple clients with same session_id will share position tracking
4. **Server Restart**: Position tracking persists across server restarts
5. **Invalid Positions**: Negative or invalid positions are normalized to 0

This enhancement makes the `/device_logs` endpoint much more user-friendly while maintaining full backward compatibility.
